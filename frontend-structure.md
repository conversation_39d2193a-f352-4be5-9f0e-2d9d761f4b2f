# Freela Syria - Frontend Architecture

## 🏗️ Frontend Technology Stack

This document outlines the frontend architecture for Freela Syria, covering the React Native mobile app and Next.js web applications with a focus on Arabic-first development and optimal performance.

## 📱 Mobile App Architecture (React Native)

### Core Technologies
- **React Native 0.72+**: Latest stable version with New Architecture
- **TypeScript**: Full type safety and developer experience
- **Expo SDK 49+**: Managed workflow with custom native modules
- **React Navigation 6**: Type-safe navigation with deep linking
- **Zustand**: Lightweight state management
- **React Query**: Server state management and caching
- **React Hook Form**: Performant form handling with validation

### Project Structure
```
src/
├── components/           # Reusable UI components
│   ├── ui/              # Basic UI elements
│   │   ├── Button/
│   │   ├── Input/
│   │   ├── Card/
│   │   ├── Modal/
│   │   └── index.ts
│   ├── forms/           # Form-specific components
│   ├── navigation/      # Navigation components
│   └── layout/          # Layout components
├── screens/             # Screen components
│   ├── auth/           # Authentication screens
│   ├── onboarding/     # User onboarding flow
│   ├── expert/         # Expert-specific screens
│   ├── client/         # Client-specific screens
│   ├── chat/           # Messaging interface
│   ├── booking/        # Booking management
│   └── profile/        # Profile management
├── navigation/         # Navigation configuration
├── services/           # API and external services
├── store/              # State management
├── hooks/              # Custom React hooks
├── utils/              # Utility functions
├── constants/          # App constants
├── types/              # TypeScript type definitions
└── assets/             # Static assets
```

### State Management Architecture

#### Zustand Store Structure
```typescript
// stores/authStore.ts
interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
}

// stores/expertStore.ts
interface ExpertState {
  profile: ExpertProfile | null;
  services: Service[];
  bookings: Booking[];
  analytics: ExpertAnalytics | null;
  updateProfile: (data: Partial<ExpertProfile>) => Promise<void>;
  createService: (service: CreateServiceData) => Promise<void>;
}

// stores/clientStore.ts
interface ClientState {
  searchResults: Expert[];
  filters: SearchFilters;
  favorites: Expert[];
  bookings: Booking[];
  searchExperts: (query: string, filters: SearchFilters) => Promise<void>;
  bookService: (expertId: string, serviceId: string, data: BookingData) => Promise<void>;
}
```

#### React Query Configuration
```typescript
// services/queryClient.ts
import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: 3,
      refetchOnWindowFocus: false,
    },
    mutations: {
      retry: 1,
    },
  },
});

// Custom hooks for API calls
export const useExperts = (filters: SearchFilters) => {
  return useQuery({
    queryKey: ['experts', filters],
    queryFn: () => api.experts.search(filters),
    enabled: !!filters.category,
  });
};

export const useCreateService = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: api.services.create,
    onSuccess: () => {
      queryClient.invalidateQueries(['services']);
    },
  });
};
```

### Component Architecture

#### UI Component System
```typescript
// components/ui/Button/Button.tsx
interface ButtonProps {
  variant: 'primary' | 'secondary' | 'ghost';
  size: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  onPress: () => void;
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  children,
  onPress,
  disabled = false,
  loading = false,
  icon,
}) => {
  const styles = useButtonStyles(variant, size);
  
  return (
    <Pressable
      style={[styles.button, disabled && styles.disabled]}
      onPress={onPress}
      disabled={disabled || loading}
      accessibilityRole="button"
      accessibilityState={{ disabled }}
    >
      {loading ? (
        <ActivityIndicator color={styles.text.color} />
      ) : (
        <>
          {icon && <View style={styles.icon}>{icon}</View>}
          <Text style={styles.text}>{children}</Text>
        </>
      )}
    </Pressable>
  );
};
```

#### Form Components with Arabic Support
```typescript
// components/forms/TextInput/TextInput.tsx
interface TextInputProps {
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  error?: string;
  multiline?: boolean;
  textDirection?: 'ltr' | 'rtl' | 'auto';
  keyboardType?: KeyboardTypeOptions;
}

export const TextInput: React.FC<TextInputProps> = ({
  label,
  value,
  onChangeText,
  placeholder,
  error,
  multiline = false,
  textDirection = 'auto',
  keyboardType = 'default',
}) => {
  const { colors, spacing } = useTheme();
  const { t, isRTL } = useTranslation();
  
  return (
    <View style={styles.container}>
      <Text style={[styles.label, { textAlign: isRTL ? 'right' : 'left' }]}>
        {label}
      </Text>
      <RNTextInput
        style={[
          styles.input,
          { textAlign: isRTL ? 'right' : 'left' },
          error && styles.inputError,
        ]}
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        placeholderTextColor={colors.textMuted}
        multiline={multiline}
        textAlignVertical={multiline ? 'top' : 'center'}
        keyboardType={keyboardType}
        writingDirection={textDirection}
      />
      {error && (
        <Text style={styles.errorText}>{error}</Text>
      )}
    </View>
  );
};
```

### Navigation Structure

#### Navigation Configuration
```typescript
// navigation/AppNavigator.tsx
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  ExpertOnboarding: undefined;
  ServiceDetails: { serviceId: string };
  Chat: { conversationId: string };
  Booking: { expertId: string; serviceId: string };
};

export type MainTabParamList = {
  Home: undefined;
  Search: undefined;
  Bookings: undefined;
  Chat: undefined;
  Profile: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

export const AppNavigator = () => {
  const { isAuthenticated } = useAuthStore();
  
  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {!isAuthenticated ? (
          <Stack.Screen name="Auth" component={AuthNavigator} />
        ) : (
          <>
            <Stack.Screen name="Main" component={MainTabNavigator} />
            <Stack.Screen 
              name="ServiceDetails" 
              component={ServiceDetailsScreen}
              options={{ presentation: 'modal' }}
            />
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};
```

### Internationalization (i18n)

#### Translation Setup
```typescript
// i18n/config.ts
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import * as Localization from 'expo-localization';

import ar from './locales/ar.json';
import en from './locales/en.json';

i18n
  .use(initReactI18next)
  .init({
    resources: {
      ar: { translation: ar },
      en: { translation: en },
    },
    lng: Localization.locale.startsWith('ar') ? 'ar' : 'en',
    fallbackLng: 'ar',
    interpolation: {
      escapeValue: false,
    },
  });

export default i18n;

// Custom hook for RTL support
export const useTranslation = () => {
  const { t, i18n } = useReactI18next();
  const isRTL = i18n.language === 'ar';
  
  return { t, isRTL, changeLanguage: i18n.changeLanguage };
};
```

#### Arabic Locale Files
```json
// i18n/locales/ar.json
{
  "auth": {
    "login": "تسجيل الدخول",
    "register": "إنشاء حساب",
    "email": "البريد الإلكتروني",
    "password": "كلمة المرور",
    "forgotPassword": "نسيت كلمة المرور؟"
  },
  "expert": {
    "createProfile": "إنشاء الملف الشخصي",
    "addService": "إضافة خدمة",
    "manageBookings": "إدارة الحجوزات",
    "earnings": "الأرباح"
  },
  "client": {
    "findExperts": "البحث عن خبراء",
    "bookService": "حجز الخدمة",
    "myBookings": "حجوزاتي",
    "favorites": "المفضلة"
  }
}
```

---

## 🌐 Web Applications Architecture (Next.js)

### Admin Dashboard

#### Technology Stack
- **Next.js 14**: App Router with Server Components
- **TypeScript**: Full type safety
- **Tailwind CSS**: Utility-first styling
- **Shadcn/ui**: High-quality component library
- **React Hook Form**: Form handling with Zod validation
- **Recharts**: Data visualization
- **NextAuth.js**: Authentication

#### Project Structure
```
src/
├── app/                 # Next.js App Router
│   ├── (auth)/         # Authentication routes
│   ├── dashboard/      # Main dashboard
│   ├── users/          # User management
│   ├── services/       # Service management
│   ├── payments/       # Payment tracking
│   ├── analytics/      # Analytics dashboard
│   └── layout.tsx      # Root layout
├── components/         # React components
│   ├── ui/            # Shadcn/ui components
│   ├── forms/         # Form components
│   ├── tables/        # Data table components
│   ├── charts/        # Chart components
│   └── layout/        # Layout components
├── lib/               # Utility libraries
├── hooks/             # Custom React hooks
├── types/             # TypeScript types
└── styles/            # Global styles
```

#### Component Examples
```typescript
// components/tables/UsersTable.tsx
interface UsersTableProps {
  users: User[];
  onUserSelect: (user: User) => void;
  onUserDelete: (userId: string) => void;
}

export const UsersTable: React.FC<UsersTableProps> = ({
  users,
  onUserSelect,
  onUserDelete,
}) => {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [filtering, setFiltering] = useState('');

  const table = useReactTable({
    data: users,
    columns: userColumns,
    state: { sorting, globalFilter: filtering },
    onSortingChange: setSorting,
    onGlobalFilterChange: setFiltering,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
  });

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Input
          placeholder="البحث في المستخدمين..."
          value={filtering}
          onChange={(e) => setFiltering(e.target.value)}
          className="max-w-sm"
        />
        <Button onClick={() => onUserSelect(null)}>
          إضافة مستخدم جديد
        </Button>
      </div>
      
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {flexRender(
                      header.column.columnDef.header,
                      header.getContext()
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows.map((row) => (
              <TableRow key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id}>
                    {flexRender(
                      cell.column.columnDef.cell,
                      cell.getContext()
                    )}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};
```

### Expert Dashboard

#### Specialized Features
- **Portfolio Builder**: Drag-and-drop portfolio creation
- **Advanced Analytics**: Revenue forecasting and client insights
- **Calendar Management**: Integrated booking calendar
- **Performance Metrics**: Detailed performance tracking

#### Key Components
```typescript
// components/analytics/RevenueChart.tsx
export const RevenueChart: React.FC<{ data: RevenueData[] }> = ({ data }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>إجمالي الإيرادات</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip />
            <Line 
              type="monotone" 
              dataKey="revenue" 
              stroke="#00D4AA" 
              strokeWidth={2}
            />
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};
```

---

## 🎨 Styling and Theming

### Mobile App Styling (StyleSheet)
```typescript
// styles/theme.ts
export const theme = {
  colors: {
    primary: '#00D4AA',
    secondary: '#FF6B6B',
    background: '#0F0F0F',
    surface: '#1A1A1A',
    text: '#FFFFFF',
    textSecondary: '#B0B0B0',
    error: '#FF6B6B',
    success: '#51CF66',
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
  },
  typography: {
    h1: { fontSize: 32, fontWeight: 'bold' },
    h2: { fontSize: 24, fontWeight: 'bold' },
    body: { fontSize: 16, fontWeight: 'normal' },
    caption: { fontSize: 12, fontWeight: 'normal' },
  },
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
  },
};

// Custom hook for theme access
export const useTheme = () => theme;
```

### Web Styling (Tailwind CSS)
```css
/* styles/globals.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 15 15 15;
    --foreground: 255 255 255;
    --primary: 0 212 170;
    --secondary: 255 107 107;
    --muted: 108 108 108;
    --border: 42 42 42;
  }
  
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  [dir="rtl"] {
    text-align: right;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90;
  }
  
  .card {
    @apply bg-card text-card-foreground rounded-lg border shadow-sm;
  }
}
```

---

## 🔧 Performance Optimization

### Mobile App Optimization
- **Code Splitting**: Lazy load screens and components
- **Image Optimization**: WebP format with fallbacks
- **Bundle Analysis**: Regular bundle size monitoring
- **Memory Management**: Proper cleanup of listeners and timers
- **Network Optimization**: Request batching and caching

### Web App Optimization
- **Server Components**: Leverage Next.js Server Components
- **Static Generation**: Pre-generate static pages where possible
- **Image Optimization**: Next.js Image component with optimization
- **Bundle Splitting**: Automatic code splitting
- **Caching**: Aggressive caching strategies

---

*This frontend architecture ensures Freela Syria delivers exceptional user experiences across all platforms while maintaining code quality and performance standards.*
