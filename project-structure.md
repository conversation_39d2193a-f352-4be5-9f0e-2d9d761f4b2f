# Freela Syria - Project Structure

## 🏗️ Monorepo Architecture

This document outlines the complete project structure for Freela Syria, organized as a monorepo to facilitate code sharing, consistent tooling, and streamlined development workflows.

## 📁 Root Directory Structure

```
freela-syria/
├── apps/                          # Application packages
│   ├── mobile/                    # React Native mobile app
│   ├── admin-dashboard/           # Next.js admin web app
│   ├── expert-dashboard/          # Next.js expert web app
│   ├── marketing-site/            # Next.js marketing website
│   └── api/                       # Node.js backend API
├── packages/                      # Shared packages
│   ├── ui/                        # Shared UI components
│   ├── utils/                     # Shared utilities
│   ├── types/                     # TypeScript type definitions
│   ├── config/                    # Shared configuration
│   ├── database/                  # Database schema and migrations
│   └── i18n/                      # Internationalization
├── tools/                         # Development tools and scripts
│   ├── build/                     # Build scripts and configurations
│   ├── deploy/                    # Deployment scripts
│   └── scripts/                   # Utility scripts
├── docs/                          # Documentation
├── .github/                       # GitHub workflows and templates
├── docker/                        # Docker configurations
├── package.json                   # Root package.json
├── turbo.json                     # Turborepo configuration
├── tsconfig.json                  # Root TypeScript configuration
└── README.md                      # Project overview
```

---

## 📱 Mobile App Structure (`apps/mobile/`)

```
mobile/
├── src/
│   ├── components/                # Reusable UI components
│   │   ├── common/               # Generic components
│   │   ├── forms/                # Form-specific components
│   │   ├── navigation/           # Navigation components
│   │   └── modals/               # Modal components
│   ├── screens/                  # Screen components
│   │   ├── auth/                 # Authentication screens
│   │   ├── expert/               # Expert-specific screens
│   │   ├── client/               # Client-specific screens
│   │   ├── chat/                 # Messaging screens
│   │   ├── booking/              # Booking-related screens
│   │   └── profile/              # Profile management screens
│   ├── navigation/               # Navigation configuration
│   ├── services/                 # API and external services
│   ├── store/                    # State management
│   ├── utils/                    # Utility functions
│   ├── hooks/                    # Custom React hooks
│   ├── constants/                # App constants
│   ├── types/                    # TypeScript types
│   └── assets/                   # Images, fonts, etc.
├── android/                      # Android-specific code
├── ios/                          # iOS-specific code
├── __tests__/                    # Test files
├── package.json
└── metro.config.js               # Metro bundler configuration
```

### Key Mobile App Features
- **React Native 0.72+** with TypeScript
- **React Navigation 6** for navigation
- **Zustand** for state management
- **React Query** for server state
- **React Hook Form** for form handling
- **React Native Reanimated** for animations
- **Firebase** for push notifications and analytics

---

## 🌐 Admin Dashboard Structure (`apps/admin-dashboard/`)

```
admin-dashboard/
├── src/
│   ├── app/                      # Next.js App Router
│   │   ├── (auth)/              # Authentication routes
│   │   ├── dashboard/           # Main dashboard routes
│   │   ├── users/               # User management
│   │   ├── listings/            # Service listing management
│   │   ├── payments/            # Payment tracking
│   │   ├── analytics/           # Analytics and reports
│   │   └── settings/            # System settings
│   ├── components/              # React components
│   │   ├── ui/                  # Basic UI components
│   │   ├── forms/               # Form components
│   │   ├── tables/              # Data table components
│   │   ├── charts/              # Chart components
│   │   └── layout/              # Layout components
│   ├── lib/                     # Utility libraries
│   ├── hooks/                   # Custom React hooks
│   ├── types/                   # TypeScript types
│   └── styles/                  # CSS and styling
├── public/                      # Static assets
├── __tests__/                   # Test files
├── package.json
├── next.config.js               # Next.js configuration
└── tailwind.config.js           # Tailwind CSS configuration
```

### Key Admin Dashboard Features
- **Next.js 14** with App Router and TypeScript
- **Tailwind CSS** for styling
- **Shadcn/ui** for component library
- **React Hook Form** with Zod validation
- **Recharts** for data visualization
- **NextAuth.js** for authentication

---

## 🎯 Expert Dashboard Structure (`apps/expert-dashboard/`)

```
expert-dashboard/
├── src/
│   ├── app/                     # Next.js App Router
│   │   ├── (auth)/             # Authentication routes
│   │   ├── dashboard/          # Main dashboard
│   │   ├── profile/            # Profile management
│   │   ├── services/           # Service management
│   │   ├── bookings/           # Booking management
│   │   ├── analytics/          # Performance analytics
│   │   ├── payments/           # Payment history
│   │   └── settings/           # Account settings
│   ├── components/             # React components
│   ├── lib/                    # Utility libraries
│   ├── hooks/                  # Custom React hooks
│   ├── types/                  # TypeScript types
│   └── styles/                 # CSS and styling
├── public/                     # Static assets
├── package.json
└── next.config.js              # Next.js configuration
```

---

## 🚀 Marketing Site Structure (`apps/marketing-site/`)

```
marketing-site/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── (ar)/              # Arabic version
│   │   ├── (en)/              # English version
│   │   ├── blog/              # Blog posts
│   │   ├── help/              # Help center
│   │   └── legal/             # Legal pages
│   ├── components/            # React components
│   ├── content/               # MDX content
│   ├── lib/                   # Utility libraries
│   └── styles/                # CSS and styling
├── public/                    # Static assets
├── package.json
└── next.config.js             # Next.js configuration
```

---

## 🔧 Backend API Structure (`apps/api/`)

```
api/
├── src/
│   ├── controllers/           # Route controllers
│   │   ├── auth.controller.ts
│   │   ├── users.controller.ts
│   │   ├── experts.controller.ts
│   │   ├── services.controller.ts
│   │   ├── bookings.controller.ts
│   │   ├── payments.controller.ts
│   │   ├── chat.controller.ts
│   │   └── admin.controller.ts
│   ├── middleware/            # Express middleware
│   │   ├── auth.middleware.ts
│   │   ├── validation.middleware.ts
│   │   ├── rate-limit.middleware.ts
│   │   └── error.middleware.ts
│   ├── routes/                # API routes
│   ├── services/              # Business logic services
│   │   ├── auth.service.ts
│   │   ├── user.service.ts
│   │   ├── expert.service.ts
│   │   ├── booking.service.ts
│   │   ├── payment.service.ts
│   │   ├── ai.service.ts
│   │   └── notification.service.ts
│   ├── models/                # Database models (Prisma)
│   ├── utils/                 # Utility functions
│   ├── config/                # Configuration files
│   ├── types/                 # TypeScript types
│   └── tests/                 # Test files
├── prisma/                    # Prisma schema and migrations
├── docker/                    # Docker configurations
├── package.json
└── tsconfig.json
```

### Key Backend Features
- **Node.js** with **Express/Fastify**
- **TypeScript** for type safety
- **Prisma ORM** with PostgreSQL
- **Redis** for caching and sessions
- **JWT** for authentication
- **WebSocket** for real-time features
- **Bull Queue** for background jobs

---

## 📦 Shared Packages

### UI Package (`packages/ui/`)
```
ui/
├── src/
│   ├── components/            # Shared UI components
│   │   ├── Button/
│   │   ├── Input/
│   │   ├── Modal/
│   │   ├── Card/
│   │   └── index.ts
│   ├── icons/                 # Icon components
│   ├── themes/                # Theme configurations
│   └── styles/                # Shared styles
├── package.json
└── tsconfig.json
```

### Utils Package (`packages/utils/`)
```
utils/
├── src/
│   ├── validation/            # Validation schemas
│   ├── formatting/            # Data formatting utilities
│   ├── constants/             # Shared constants
│   ├── helpers/               # Helper functions
│   └── index.ts
├── package.json
└── tsconfig.json
```

### Types Package (`packages/types/`)
```
types/
├── src/
│   ├── api/                   # API type definitions
│   ├── database/              # Database type definitions
│   ├── ui/                    # UI component types
│   └── index.ts
├── package.json
└── tsconfig.json
```

### Database Package (`packages/database/`)
```
database/
├── prisma/
│   ├── schema.prisma          # Database schema
│   ├── migrations/            # Database migrations
│   └── seed.ts                # Database seeding
├── src/
│   ├── client.ts              # Prisma client configuration
│   └── types.ts               # Generated types
├── package.json
└── tsconfig.json
```

### i18n Package (`packages/i18n/`)
```
i18n/
├── locales/
│   ├── ar/                    # Arabic translations
│   │   ├── common.json
│   │   ├── auth.json
│   │   ├── expert.json
│   │   └── client.json
│   └── en/                    # English translations
├── src/
│   ├── config.ts              # i18n configuration
│   ├── utils.ts               # Translation utilities
│   └── index.ts
├── package.json
└── tsconfig.json
```

---

## 🛠️ Development Tools

### Build Tools (`tools/build/`)
- **Turborepo** for monorepo build orchestration
- **ESBuild** for fast TypeScript compilation
- **Webpack** configurations for web apps
- **Metro** configuration for React Native

### Deployment Tools (`tools/deploy/`)
- **Docker** configurations for containerization
- **Kubernetes** manifests for orchestration
- **CI/CD** scripts for automated deployment
- **Environment** configuration management

### Scripts (`tools/scripts/`)
- Database migration and seeding scripts
- Code generation utilities
- Testing and quality assurance scripts
- Development environment setup

---

## 🔧 Configuration Files

### Root Configuration
- **package.json**: Workspace configuration and scripts
- **turbo.json**: Turborepo pipeline configuration
- **tsconfig.json**: Root TypeScript configuration
- **.eslintrc.js**: ESLint configuration
- **.prettierrc**: Prettier configuration
- **docker-compose.yml**: Local development environment

### Environment Variables
```
# Database
DATABASE_URL=postgresql://...
REDIS_URL=redis://...

# Authentication
JWT_SECRET=...
JWT_EXPIRES_IN=7d

# External Services
OPENAI_API_KEY=...
STRIPE_SECRET_KEY=...
FIREBASE_CONFIG=...

# App Configuration
NODE_ENV=development
PORT=3000
API_URL=http://localhost:3000
```

---

## 📋 Development Workflow

### Getting Started
```bash
# Clone repository
git clone https://github.com/freela-syria/platform.git
cd platform

# Install dependencies
npm install

# Set up environment
cp .env.example .env.local

# Start development servers
npm run dev
```

### Available Scripts
```bash
# Development
npm run dev              # Start all apps in development
npm run dev:mobile       # Start mobile app only
npm run dev:admin        # Start admin dashboard only
npm run dev:api          # Start API server only

# Building
npm run build            # Build all apps
npm run build:mobile     # Build mobile app
npm run build:web        # Build web apps

# Testing
npm run test             # Run all tests
npm run test:unit        # Run unit tests
npm run test:e2e         # Run end-to-end tests

# Code Quality
npm run lint             # Lint all code
npm run format           # Format all code
npm run type-check       # TypeScript type checking

# Database
npm run db:migrate       # Run database migrations
npm run db:seed          # Seed database with test data
npm run db:studio        # Open Prisma Studio
```

---

## 🔒 Security Considerations

### Code Organization
- Sensitive configuration in environment variables
- API keys and secrets never committed to repository
- Separate development and production configurations
- Role-based access control for different app sections

### Dependency Management
- Regular security audits with `npm audit`
- Automated dependency updates with Dependabot
- Lock file integrity verification
- Minimal dependency principle

---

*This project structure provides a scalable foundation for Freela Syria while maintaining code quality, developer experience, and deployment efficiency.*
