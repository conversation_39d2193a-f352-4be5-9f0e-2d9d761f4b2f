# Freela Syria - Payment Methods & Financial Architecture

## 💰 Payment Strategy Overview

Freela Syria implements a comprehensive payment ecosystem designed for the Syrian market, accommodating various payment preferences while ensuring platform sustainability through our hybrid monetization model.

## 🎯 Payment Philosophy

### Core Principles
- **Accessibility**: Support multiple payment methods including cash
- **Security**: Robust fraud prevention and secure transactions
- **Transparency**: Clear fee structure with no hidden costs
- **Compliance**: Adherence to local and international financial regulations
- **Flexibility**: Accommodate economic realities of Syrian market

### Hybrid Model Benefits
- **User Adoption**: Lower barrier to entry with cash options
- **Platform Growth**: Gradual transition to digital payments
- **Revenue Assurance**: Guaranteed platform fees through subscription model
- **Market Adaptation**: Flexible approach to local payment preferences

---

## 💳 Supported Payment Methods

### 1. Digital Payment Methods

#### Credit/Debit Cards
```typescript
// Stripe integration for card payments
export class StripePaymentService {
  private stripe: Stripe;

  constructor() {
    this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2023-10-16',
    });
  }

  async createPaymentIntent(amount: number, currency: string, metadata: any): Promise<PaymentIntent> {
    return await this.stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to cents
      currency: currency.toLowerCase(),
      metadata,
      automatic_payment_methods: {
        enabled: true,
      },
    });
  }

  async processPayment(paymentIntentId: string): Promise<PaymentResult> {
    const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);
    
    return {
      success: paymentIntent.status === 'succeeded',
      transactionId: paymentIntent.id,
      amount: paymentIntent.amount / 100,
      currency: paymentIntent.currency,
      fees: this.calculateStripeFees(paymentIntent.amount),
    };
  }
}
```

#### PayPal Integration
```typescript
// PayPal payment processing
export class PayPalPaymentService {
  private paypal: PayPalApi;

  async createOrder(amount: number, currency: string): Promise<PayPalOrder> {
    const request = new paypal.orders.OrdersCreateRequest();
    request.prefer("return=representation");
    request.requestBody({
      intent: 'CAPTURE',
      purchase_units: [{
        amount: {
          currency_code: currency,
          value: amount.toString(),
        }
      }],
      application_context: {
        return_url: `${process.env.FRONTEND_URL}/payment/success`,
        cancel_url: `${process.env.FRONTEND_URL}/payment/cancel`,
      }
    });

    const order = await this.paypal.execute(request);
    return order.result;
  }

  async capturePayment(orderId: string): Promise<PaymentResult> {
    const request = new paypal.orders.OrdersCaptureRequest(orderId);
    const capture = await this.paypal.execute(request);
    
    return {
      success: capture.result.status === 'COMPLETED',
      transactionId: capture.result.id,
      amount: parseFloat(capture.result.purchase_units[0].payments.captures[0].amount.value),
      currency: capture.result.purchase_units[0].payments.captures[0].amount.currency_code,
    };
  }
}
```

#### Cryptocurrency Payments
```typescript
// Crypto payment integration
export class CryptoPaymentService {
  private supportedCurrencies = ['BTC', 'ETH', 'USDT', 'USDC'];

  async createCryptoPayment(amount: number, currency: string, cryptoCurrency: string): Promise<CryptoPayment> {
    // Integration with crypto payment processor (e.g., CoinGate, BitPay)
    const paymentRequest = {
      price_amount: amount,
      price_currency: currency,
      receive_currency: cryptoCurrency,
      title: 'Freela Syria Service Payment',
      description: 'Payment for freelance services',
      callback_url: `${process.env.API_URL}/webhooks/crypto-payment`,
      success_url: `${process.env.FRONTEND_URL}/payment/crypto-success`,
      cancel_url: `${process.env.FRONTEND_URL}/payment/cancel`,
    };

    const payment = await this.cryptoGateway.createPayment(paymentRequest);
    
    return {
      paymentId: payment.id,
      paymentUrl: payment.payment_url,
      address: payment.payment_address,
      amount: payment.pay_amount,
      currency: payment.pay_currency,
      expiresAt: new Date(payment.created_at + 30 * 60 * 1000), // 30 minutes
    };
  }
}
```

### 2. Local Payment Methods

#### Syrian Bank Transfers
```typescript
// Local bank integration
export class SyrianBankService {
  private supportedBanks = [
    'Commercial Bank of Syria',
    'Real Estate Bank',
    'Agricultural Cooperative Bank',
    'Industrial Bank',
    'Popular Credit Bank'
  ];

  async initiateBankTransfer(transferData: BankTransferData): Promise<BankTransferResult> {
    // Integration with local banking APIs or manual verification system
    const transfer = await this.createTransferRequest({
      fromAccount: transferData.fromAccount,
      toAccount: process.env.PLATFORM_BANK_ACCOUNT,
      amount: transferData.amount,
      currency: 'SYP',
      reference: `FREELA-${transferData.bookingId}`,
      description: 'Freela Syria Service Payment',
    });

    return {
      transferId: transfer.id,
      reference: transfer.reference,
      status: 'pending_verification',
      estimatedProcessingTime: '1-3 business days',
      instructions: this.generateTransferInstructions(transfer),
    };
  }

  private generateTransferInstructions(transfer: BankTransfer): TransferInstructions {
    return {
      bankName: 'Commercial Bank of Syria',
      accountNumber: process.env.PLATFORM_BANK_ACCOUNT,
      accountName: 'Freela Syria Platform',
      amount: transfer.amount,
      reference: transfer.reference,
      arabicInstructions: `
        تعليمات التحويل المصرفي:
        1. توجه إلى أقرب فرع للمصرف التجاري السوري
        2. اطلب تحويل مبلغ ${transfer.amount} ليرة سورية
        3. رقم الحساب: ${process.env.PLATFORM_BANK_ACCOUNT}
        4. اسم الحساب: فريلا سوريا
        5. رقم المرجع: ${transfer.reference}
        6. احتفظ بإيصال التحويل وارفعه في التطبيق
      `,
    };
  }
}
```

### 3. Cash Payment System

#### Cash Transaction Tracking
```typescript
// Cash payment management
export class CashPaymentService {
  async recordCashPayment(bookingId: string, amount: number, expertId: string): Promise<CashPaymentRecord> {
    // Check expert's cash payment limit
    const expert = await prisma.expertProfile.findUnique({
      where: { id: expertId },
      select: { cashPaymentCount: true, subscriptionTier: true }
    });

    if (expert.subscriptionTier === 'FREE' && expert.cashPaymentCount >= 3) {
      throw new PaymentError('Cash payment limit exceeded. Please upgrade to continue.');
    }

    // Record cash payment
    const cashPayment = await prisma.cashPayment.create({
      data: {
        bookingId,
        expertId,
        amount,
        currency: 'USD',
        status: 'completed',
        recordedAt: new Date(),
      }
    });

    // Update expert's cash payment count
    await prisma.expertProfile.update({
      where: { id: expertId },
      data: { cashPaymentCount: { increment: 1 } }
    });

    // Check if expert needs to pay platform fee
    if (expert.cashPaymentCount + 1 >= 3 && expert.subscriptionTier === 'FREE') {
      await this.triggerPlatformFeePayment(expertId);
    }

    return {
      id: cashPayment.id,
      amount: cashPayment.amount,
      status: cashPayment.status,
      remainingCashPayments: Math.max(0, 3 - (expert.cashPaymentCount + 1)),
      requiresSubscription: expert.cashPaymentCount + 1 >= 3,
    };
  }

  private async triggerPlatformFeePayment(expertId: string): Promise<void> {
    // Calculate platform fee for cash payments
    const cashPayments = await prisma.cashPayment.findMany({
      where: { expertId, status: 'completed' },
      orderBy: { recordedAt: 'desc' },
      take: 3,
    });

    const totalAmount = cashPayments.reduce((sum, payment) => sum + payment.amount, 0);
    const platformFee = totalAmount * 0.05; // 5% platform fee

    // Create offline payment request
    await prisma.offlinePaymentRequest.create({
      data: {
        expertId,
        amount: platformFee,
        currency: 'USD',
        type: 'platform_fee',
        status: 'pending',
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        description: 'Platform fee for cash payments',
      }
    });

    // Notify expert about required payment
    await this.notificationService.sendPlatformFeeNotification(expertId, platformFee);
  }
}
```

#### Offline Payment Verification
```typescript
// Admin verification system for offline payments
export class OfflinePaymentVerification {
  async verifyOfflinePayment(paymentId: string, adminId: string, verificationData: VerificationData): Promise<VerificationResult> {
    const payment = await prisma.offlinePaymentRequest.findUnique({
      where: { id: paymentId },
      include: { expert: { include: { user: true } } }
    });

    if (!payment) {
      throw new Error('Payment request not found');
    }

    // Update payment status
    const updatedPayment = await prisma.offlinePaymentRequest.update({
      where: { id: paymentId },
      data: {
        status: verificationData.approved ? 'verified' : 'rejected',
        verifiedBy: adminId,
        verifiedAt: new Date(),
        verificationNotes: verificationData.notes,
        receiptUrl: verificationData.receiptUrl,
      }
    });

    if (verificationData.approved) {
      // Reset expert's cash payment count
      await prisma.expertProfile.update({
        where: { id: payment.expertId },
        data: { cashPaymentCount: 0 }
      });

      // Notify expert of successful verification
      await this.notificationService.sendPaymentVerificationSuccess(payment.expertId);
    } else {
      // Handle rejection - may suspend expert account
      await this.handlePaymentRejection(payment.expertId, verificationData.notes);
    }

    return {
      success: verificationData.approved,
      paymentId: payment.id,
      newStatus: updatedPayment.status,
      expertNotified: true,
    };
  }

  private async handlePaymentRejection(expertId: string, reason: string): Promise<void> {
    // Suspend expert account until payment is resolved
    await prisma.expertProfile.update({
      where: { id: expertId },
      data: { isAvailable: false }
    });

    // Send notification with rejection reason
    await this.notificationService.sendPaymentRejectionNotification(expertId, reason);
  }
}
```

---

## 💰 Subscription & Monetization System

### Subscription Tiers
```typescript
// Subscription management
export class SubscriptionService {
  private tiers = {
    FREE: {
      name: 'مجاني',
      price: 0,
      currency: 'USD',
      features: {
        maxConversations: 5,
        maxCashPayments: 3,
        featuredListing: false,
        prioritySupport: false,
        advancedAnalytics: false,
      }
    },
    PRO: {
      name: 'احترافي',
      price: 15,
      currency: 'USD',
      features: {
        maxConversations: -1, // Unlimited
        maxCashPayments: -1,  // Unlimited
        featuredListing: true,
        prioritySupport: true,
        advancedAnalytics: true,
      }
    },
    PREMIUM: {
      name: 'مميز',
      price: 30,
      currency: 'USD',
      features: {
        maxConversations: -1,
        maxCashPayments: -1,
        featuredListing: true,
        prioritySupport: true,
        advancedAnalytics: true,
        customBranding: true,
        apiAccess: true,
      }
    }
  };

  async upgradeSubscription(expertId: string, tier: string, paymentMethodId: string): Promise<SubscriptionResult> {
    const tierConfig = this.tiers[tier];
    if (!tierConfig) {
      throw new Error('Invalid subscription tier');
    }

    // Create Stripe subscription
    const subscription = await this.stripe.subscriptions.create({
      customer: await this.getOrCreateStripeCustomer(expertId),
      items: [{ price: tierConfig.stripePriceId }],
      default_payment_method: paymentMethodId,
      expand: ['latest_invoice.payment_intent'],
    });

    // Update expert profile
    await prisma.expertProfile.update({
      where: { id: expertId },
      data: {
        subscriptionTier: tier,
        subscriptionId: subscription.id,
        subscriptionStatus: subscription.status,
        subscriptionCurrentPeriodEnd: new Date(subscription.current_period_end * 1000),
      }
    });

    return {
      subscriptionId: subscription.id,
      status: subscription.status,
      currentPeriodEnd: new Date(subscription.current_period_end * 1000),
      features: tierConfig.features,
    };
  }

  async handleSubscriptionWebhook(event: Stripe.Event): Promise<void> {
    switch (event.type) {
      case 'invoice.payment_succeeded':
        await this.handleSuccessfulPayment(event.data.object as Stripe.Invoice);
        break;
      
      case 'invoice.payment_failed':
        await this.handleFailedPayment(event.data.object as Stripe.Invoice);
        break;
      
      case 'customer.subscription.deleted':
        await this.handleSubscriptionCancellation(event.data.object as Stripe.Subscription);
        break;
    }
  }
}
```

### Transaction Fee Structure
```typescript
// Fee calculation system
export class FeeCalculationService {
  private feeStructure = {
    digital: {
      stripe: 0.029 + 0.30, // 2.9% + $0.30
      paypal: 0.034 + 0.30, // 3.4% + $0.30
      crypto: 0.01,         // 1%
      bankTransfer: 0.02,   // 2%
    },
    platform: {
      standard: 0.05,       // 5% platform fee
      premium: 0.03,        // 3% for premium subscribers
    }
  };

  calculateTransactionFees(amount: number, paymentMethod: string, expertTier: string): FeeBreakdown {
    const paymentProcessorFee = this.calculatePaymentProcessorFee(amount, paymentMethod);
    const platformFeeRate = expertTier === 'PREMIUM' ? this.feeStructure.platform.premium : this.feeStructure.platform.standard;
    const platformFee = amount * platformFeeRate;
    
    const totalFees = paymentProcessorFee + platformFee;
    const expertReceives = amount - totalFees;

    return {
      transactionAmount: amount,
      paymentProcessorFee,
      platformFee,
      totalFees,
      expertReceives,
      feeBreakdown: {
        paymentProcessor: paymentProcessorFee,
        platform: platformFee,
      }
    };
  }

  private calculatePaymentProcessorFee(amount: number, method: string): number {
    const feeConfig = this.feeStructure.digital[method];
    if (typeof feeConfig === 'number') {
      return amount * feeConfig;
    }
    // For percentage + fixed fee structure
    return (amount * feeConfig.percentage) + feeConfig.fixed;
  }
}
```

---

## 🔒 Security & Compliance

### PCI DSS Compliance
```typescript
// Security measures for payment processing
export class PaymentSecurityService {
  // Tokenize sensitive payment data
  async tokenizePaymentMethod(paymentData: PaymentMethodData): Promise<PaymentToken> {
    // Never store raw card data - use Stripe tokens
    const paymentMethod = await this.stripe.paymentMethods.create({
      type: 'card',
      card: {
        token: paymentData.stripeToken,
      },
    });

    return {
      tokenId: paymentMethod.id,
      last4: paymentMethod.card.last4,
      brand: paymentMethod.card.brand,
      expiryMonth: paymentMethod.card.exp_month,
      expiryYear: paymentMethod.card.exp_year,
    };
  }

  // Fraud detection
  async detectFraud(transactionData: TransactionData): Promise<FraudAssessment> {
    const riskFactors = await Promise.all([
      this.checkVelocityLimits(transactionData.userId),
      this.checkGeolocation(transactionData.ipAddress),
      this.checkDeviceFingerprint(transactionData.deviceId),
      this.checkAmountPatterns(transactionData.amount, transactionData.userId),
    ]);

    const riskScore = this.calculateRiskScore(riskFactors);
    
    return {
      riskScore,
      decision: this.makeDecision(riskScore),
      factors: riskFactors,
      requiresManualReview: riskScore > 0.7,
    };
  }
}
```

### Anti-Money Laundering (AML)
```typescript
// AML compliance monitoring
export class AMLComplianceService {
  async monitorTransaction(transaction: Transaction): Promise<AMLResult> {
    const checks = await Promise.all([
      this.checkSanctionsList(transaction.userId),
      this.checkTransactionLimits(transaction),
      this.checkSuspiciousPatterns(transaction),
      this.checkSourceOfFunds(transaction),
    ]);

    const suspiciousActivity = checks.some(check => check.flagged);
    
    if (suspiciousActivity) {
      await this.fileSuspiciousActivityReport(transaction, checks);
    }

    return {
      cleared: !suspiciousActivity,
      flags: checks.filter(check => check.flagged),
      requiresReporting: suspiciousActivity,
    };
  }

  private async checkTransactionLimits(transaction: Transaction): Promise<ComplianceCheck> {
    const dailyLimit = 10000; // $10,000 USD
    const monthlyLimit = 50000; // $50,000 USD

    const dailyTotal = await this.getDailyTransactionTotal(transaction.userId);
    const monthlyTotal = await this.getMonthlyTransactionTotal(transaction.userId);

    return {
      flagged: (dailyTotal + transaction.amount > dailyLimit) || 
               (monthlyTotal + transaction.amount > monthlyLimit),
      reason: 'Transaction limits exceeded',
      details: { dailyTotal, monthlyTotal, limits: { daily: dailyLimit, monthly: monthlyLimit } }
    };
  }
}
```

---

## 📊 Payment Analytics & Reporting

### Revenue Tracking
```typescript
// Payment analytics service
export class PaymentAnalyticsService {
  async generateRevenueReport(period: 'daily' | 'weekly' | 'monthly', startDate: Date, endDate: Date): Promise<RevenueReport> {
    const transactions = await prisma.payment.findMany({
      where: {
        createdAt: { gte: startDate, lte: endDate },
        status: 'completed',
      },
      include: {
        booking: {
          include: {
            service: { include: { expert: true } },
            client: true,
          }
        }
      }
    });

    const analytics = {
      totalRevenue: this.calculateTotalRevenue(transactions),
      platformFees: this.calculatePlatformFees(transactions),
      paymentMethodBreakdown: this.analyzePaymentMethods(transactions),
      expertEarnings: this.calculateExpertEarnings(transactions),
      transactionVolume: transactions.length,
      averageTransactionValue: this.calculateAverageTransaction(transactions),
      topCategories: this.analyzeTopCategories(transactions),
      geographicDistribution: this.analyzeGeography(transactions),
    };

    return {
      period,
      startDate,
      endDate,
      analytics,
      trends: await this.calculateTrends(analytics, period),
    };
  }

  async trackPaymentConversion(): Promise<ConversionMetrics> {
    const funnelData = await this.getPaymentFunnelData();
    
    return {
      initiationRate: funnelData.initiated / funnelData.bookings,
      completionRate: funnelData.completed / funnelData.initiated,
      abandonmentPoints: this.identifyAbandonmentPoints(funnelData),
      methodPreferences: this.analyzeMethodPreferences(funnelData),
      optimizationSuggestions: this.generateOptimizationSuggestions(funnelData),
    };
  }
}
```

---

*This comprehensive payment system ensures Freela Syria can serve the Syrian market effectively while building a sustainable business model that grows with user adoption and economic development.*
