# Freela Syria - Backend Architecture

## 🏗️ Backend Technology Stack

This document outlines the comprehensive backend architecture for Freela Syria, designed for scalability, security, and optimal performance in the Syrian market context.

## 🔧 Core Technologies

### Primary Stack
- **Runtime**: Node.js 18+ with TypeScript
- **Framework**: Fastify (high performance) with Express.js compatibility
- **Database**: PostgreSQL 15+ with Prisma ORM
- **Cache**: Redis 7+ for sessions, caching, and real-time features
- **Queue**: Bull Queue with Redis for background jobs
- **Authentication**: JWT with refresh tokens
- **File Storage**: AWS S3 compatible (MinIO for local development)
- **Real-time**: WebSocket with Socket.io
- **API Documentation**: OpenAPI 3.0 with Swagger UI

### Supporting Services
- **Email**: SendGrid for transactional emails
- **SMS**: Twilio for phone verification
- **Push Notifications**: Firebase Cloud Messaging
- **Payment Processing**: Stripe, PayPal, local payment gateways
- **AI Services**: OpenAI GPT-4 for assistant features
- **Monitoring**: Prometheus + Grafana
- **Logging**: <PERSON> with structured logging

## 📁 Project Structure

```
api/
├── src/
│   ├── controllers/           # Route controllers
│   │   ├── auth.controller.ts
│   │   ├── users.controller.ts
│   │   ├── experts.controller.ts
│   │   ├── services.controller.ts
│   │   ├── bookings.controller.ts
│   │   ├── payments.controller.ts
│   │   ├── chat.controller.ts
│   │   ├── admin.controller.ts
│   │   └── ai.controller.ts
│   ├── services/              # Business logic layer
│   │   ├── auth.service.ts
│   │   ├── user.service.ts
│   │   ├── expert.service.ts
│   │   ├── booking.service.ts
│   │   ├── payment.service.ts
│   │   ├── notification.service.ts
│   │   ├── ai.service.ts
│   │   └── analytics.service.ts
│   ├── middleware/            # Express/Fastify middleware
│   │   ├── auth.middleware.ts
│   │   ├── validation.middleware.ts
│   │   ├── rate-limit.middleware.ts
│   │   ├── cors.middleware.ts
│   │   └── error.middleware.ts
│   ├── routes/                # API route definitions
│   │   ├── auth.routes.ts
│   │   ├── users.routes.ts
│   │   ├── experts.routes.ts
│   │   ├── services.routes.ts
│   │   ├── bookings.routes.ts
│   │   ├── payments.routes.ts
│   │   ├── chat.routes.ts
│   │   └── admin.routes.ts
│   ├── models/                # Database models and schemas
│   │   ├── user.model.ts
│   │   ├── expert.model.ts
│   │   ├── service.model.ts
│   │   ├── booking.model.ts
│   │   ├── payment.model.ts
│   │   └── chat.model.ts
│   ├── utils/                 # Utility functions
│   │   ├── validation.ts
│   │   ├── encryption.ts
│   │   ├── email.ts
│   │   ├── sms.ts
│   │   └── file-upload.ts
│   ├── config/                # Configuration files
│   │   ├── database.ts
│   │   ├── redis.ts
│   │   ├── auth.ts
│   │   └── environment.ts
│   ├── types/                 # TypeScript type definitions
│   │   ├── api.types.ts
│   │   ├── database.types.ts
│   │   └── auth.types.ts
│   ├── jobs/                  # Background job processors
│   │   ├── email.jobs.ts
│   │   ├── notification.jobs.ts
│   │   ├── payment.jobs.ts
│   │   └── analytics.jobs.ts
│   └── tests/                 # Test files
│       ├── unit/
│       ├── integration/
│       └── e2e/
├── prisma/                    # Database schema and migrations
│   ├── schema.prisma
│   ├── migrations/
│   └── seed.ts
├── docker/                    # Docker configurations
├── docs/                      # API documentation
├── package.json
├── tsconfig.json
└── Dockerfile
```

## 🗄️ Database Schema Design

### Core Models

#### User Model
```typescript
// prisma/schema.prisma
model User {
  id          String   @id @default(cuid())
  email       String   @unique
  phone       String?  @unique
  password    String
  role        UserRole @default(CLIENT)
  isVerified  Boolean  @default(false)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Profile information
  firstName   String
  lastName    String
  avatar      String?
  language    String   @default("ar")
  timezone    String   @default("Asia/Damascus")
  
  // Relationships
  expertProfile ExpertProfile?
  clientProfile ClientProfile?
  bookings      Booking[]
  payments      Payment[]
  conversations Conversation[]
  reviews       Review[]
  
  @@map("users")
}

enum UserRole {
  CLIENT
  EXPERT
  ADMIN
  MODERATOR
}
```

#### Expert Profile Model
```typescript
model ExpertProfile {
  id              String   @id @default(cuid())
  userId          String   @unique
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // Professional information
  title           String
  bio             String?
  experience      Int?     // Years of experience
  hourlyRate      Decimal?
  isAvailable     Boolean  @default(true)
  responseTime    Int?     // Average response time in minutes
  
  // Verification and quality
  isVerified      Boolean  @default(false)
  verificationLevel String @default("BASIC") // BASIC, VERIFIED, PREMIUM
  rating          Decimal? @default(0)
  totalReviews    Int      @default(0)
  completedJobs   Int      @default(0)
  
  // Subscription and limits
  subscriptionTier String  @default("FREE") // FREE, PRO, PREMIUM
  conversationCount Int    @default(0)
  cashPaymentCount Int     @default(0)
  
  // Location and availability
  city            String?
  country         String   @default("Syria")
  workingHours    Json?    // Flexible working hours structure
  
  // Relationships
  services        Service[]
  bookings        Booking[]
  portfolio       PortfolioItem[]
  
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  @@map("expert_profiles")
}
```

#### Service Model
```typescript
model Service {
  id              String   @id @default(cuid())
  expertId        String
  expert          ExpertProfile @relation(fields: [expertId], references: [id], onDelete: Cascade)
  
  // Service details
  title           String
  description     String
  category        String
  subcategory     String?
  tags            String[] // Array of tags for search
  
  // Pricing
  priceType       PriceType @default(FIXED)
  basePrice       Decimal
  currency        String    @default("USD")
  
  // Service configuration
  duration        Int?      // Duration in minutes
  isActive        Boolean   @default(true)
  deliveryTime    Int?      // Delivery time in days
  revisions       Int?      // Number of revisions included
  
  // Media
  images          String[]  // Array of image URLs
  videos          String[]  // Array of video URLs
  
  // Analytics
  viewCount       Int       @default(0)
  bookingCount    Int       @default(0)
  
  // Relationships
  bookings        Booking[]
  
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  
  @@map("services")
}

enum PriceType {
  FIXED
  HOURLY
  PACKAGE
}
```

#### Booking Model
```typescript
model Booking {
  id              String        @id @default(cuid())
  clientId        String
  client          User          @relation(fields: [clientId], references: [id])
  expertId        String
  expert          ExpertProfile @relation(fields: [expertId], references: [id])
  serviceId       String
  service         Service       @relation(fields: [serviceId], references: [id])
  
  // Booking details
  status          BookingStatus @default(PENDING)
  scheduledAt     DateTime?
  startedAt       DateTime?
  completedAt     DateTime?
  cancelledAt     DateTime?
  
  // Pricing
  agreedPrice     Decimal
  currency        String        @default("USD")
  paymentMethod   PaymentMethod
  
  // Communication
  requirements    String?       // Client requirements
  notes           String?       // Expert notes
  
  // Relationships
  payments        Payment[]
  conversation    Conversation?
  review          Review?
  
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  
  @@map("bookings")
}

enum BookingStatus {
  PENDING
  CONFIRMED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  DISPUTED
}

enum PaymentMethod {
  CARD
  PAYPAL
  CRYPTO
  CASH
  BANK_TRANSFER
}
```

### Advanced Models

#### Chat and Messaging
```typescript
model Conversation {
  id              String    @id @default(cuid())
  bookingId       String?   @unique
  booking         Booking?  @relation(fields: [bookingId], references: [id])
  
  // Participants
  participants    User[]
  
  // Conversation metadata
  lastMessageAt   DateTime?
  isActive        Boolean   @default(true)
  
  // Relationships
  messages        Message[]
  
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  
  @@map("conversations")
}

model Message {
  id              String       @id @default(cuid())
  conversationId  String
  conversation    Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  senderId        String
  sender          User         @relation(fields: [senderId], references: [id])
  
  // Message content
  content         String
  messageType     MessageType  @default(TEXT)
  attachments     String[]     // Array of file URLs
  
  // Message status
  isRead          Boolean      @default(false)
  readAt          DateTime?
  isEdited        Boolean      @default(false)
  editedAt        DateTime?
  
  // Moderation
  isModerated     Boolean      @default(false)
  moderationFlags String[]     // Array of moderation flags
  
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt
  
  @@map("messages")
}

enum MessageType {
  TEXT
  IMAGE
  FILE
  VOICE
  SYSTEM
}
```

## 🔐 Authentication & Authorization

### JWT Implementation
```typescript
// services/auth.service.ts
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { User } from '@prisma/client';

export class AuthService {
  private readonly JWT_SECRET = process.env.JWT_SECRET!;
  private readonly JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET!;
  private readonly JWT_EXPIRES_IN = '15m';
  private readonly JWT_REFRESH_EXPIRES_IN = '7d';

  async login(email: string, password: string): Promise<AuthResult> {
    const user = await prisma.user.findUnique({
      where: { email },
      include: { expertProfile: true, clientProfile: true }
    });

    if (!user || !await bcrypt.compare(password, user.password)) {
      throw new UnauthorizedError('Invalid credentials');
    }

    if (!user.isActive) {
      throw new ForbiddenError('Account is deactivated');
    }

    const tokens = this.generateTokens(user);
    await this.saveRefreshToken(user.id, tokens.refreshToken);

    return {
      user: this.sanitizeUser(user),
      ...tokens
    };
  }

  private generateTokens(user: User) {
    const payload = {
      userId: user.id,
      email: user.email,
      role: user.role
    };

    const accessToken = jwt.sign(payload, this.JWT_SECRET, {
      expiresIn: this.JWT_EXPIRES_IN
    });

    const refreshToken = jwt.sign(payload, this.JWT_REFRESH_SECRET, {
      expiresIn: this.JWT_REFRESH_EXPIRES_IN
    });

    return { accessToken, refreshToken };
  }

  async refreshToken(refreshToken: string): Promise<AuthResult> {
    try {
      const decoded = jwt.verify(refreshToken, this.JWT_REFRESH_SECRET) as JwtPayload;
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        include: { expertProfile: true, clientProfile: true }
      });

      if (!user || !user.isActive) {
        throw new UnauthorizedError('Invalid refresh token');
      }

      const tokens = this.generateTokens(user);
      await this.saveRefreshToken(user.id, tokens.refreshToken);

      return {
        user: this.sanitizeUser(user),
        ...tokens
      };
    } catch (error) {
      throw new UnauthorizedError('Invalid refresh token');
    }
  }
}
```

### Role-Based Access Control
```typescript
// middleware/auth.middleware.ts
export const requireAuth = async (req: FastifyRequest, reply: FastifyReply) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return reply.status(401).send({ error: 'Access token required' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as JwtPayload;
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      include: { expertProfile: true }
    });

    if (!user || !user.isActive) {
      return reply.status(401).send({ error: 'Invalid token' });
    }

    req.user = user;
  } catch (error) {
    return reply.status(401).send({ error: 'Invalid token' });
  }
};

export const requireRole = (roles: UserRole[]) => {
  return async (req: FastifyRequest, reply: FastifyReply) => {
    if (!req.user || !roles.includes(req.user.role)) {
      return reply.status(403).send({ error: 'Insufficient permissions' });
    }
  };
};

export const requireExpert = async (req: FastifyRequest, reply: FastifyReply) => {
  if (!req.user?.expertProfile) {
    return reply.status(403).send({ error: 'Expert profile required' });
  }
};
```

## 🔄 API Design Patterns

### RESTful API Structure
```typescript
// routes/experts.routes.ts
export const expertRoutes = async (fastify: FastifyInstance) => {
  // Get all experts with filtering and pagination
  fastify.get('/experts', {
    schema: {
      querystring: ExpertSearchSchema,
      response: { 200: ExpertListResponseSchema }
    }
  }, async (request, reply) => {
    const { page = 1, limit = 20, category, city, minRating } = request.query;
    
    const experts = await expertService.searchExperts({
      page,
      limit,
      filters: { category, city, minRating }
    });
    
    return reply.send(experts);
  });

  // Get expert by ID
  fastify.get('/experts/:id', {
    schema: {
      params: { id: { type: 'string' } },
      response: { 200: ExpertDetailResponseSchema }
    }
  }, async (request, reply) => {
    const expert = await expertService.getExpertById(request.params.id);
    
    if (!expert) {
      return reply.status(404).send({ error: 'Expert not found' });
    }
    
    return reply.send(expert);
  });

  // Update expert profile (authenticated)
  fastify.put('/experts/:id', {
    preHandler: [requireAuth, requireExpert],
    schema: {
      params: { id: { type: 'string' } },
      body: UpdateExpertProfileSchema,
      response: { 200: ExpertDetailResponseSchema }
    }
  }, async (request, reply) => {
    const expertId = request.params.id;
    
    // Ensure user can only update their own profile
    if (request.user.expertProfile?.id !== expertId) {
      return reply.status(403).send({ error: 'Cannot update other expert profiles' });
    }
    
    const updatedExpert = await expertService.updateExpertProfile(
      expertId,
      request.body
    );
    
    return reply.send(updatedExpert);
  });
};
```

### Input Validation with Zod
```typescript
// utils/validation.ts
import { z } from 'zod';

export const CreateServiceSchema = z.object({
  title: z.string().min(5).max(100),
  description: z.string().min(20).max(1000),
  category: z.string(),
  subcategory: z.string().optional(),
  tags: z.array(z.string()).max(10),
  priceType: z.enum(['FIXED', 'HOURLY', 'PACKAGE']),
  basePrice: z.number().positive(),
  currency: z.string().default('USD'),
  duration: z.number().positive().optional(),
  deliveryTime: z.number().positive().optional(),
  revisions: z.number().min(0).optional(),
  images: z.array(z.string().url()).max(5),
});

export const BookingCreateSchema = z.object({
  serviceId: z.string().cuid(),
  scheduledAt: z.string().datetime().optional(),
  requirements: z.string().max(500).optional(),
  paymentMethod: z.enum(['CARD', 'PAYPAL', 'CRYPTO', 'CASH', 'BANK_TRANSFER']),
});

// Validation middleware
export const validateBody = (schema: z.ZodSchema) => {
  return async (req: FastifyRequest, reply: FastifyReply) => {
    try {
      req.body = schema.parse(req.body);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          error: 'Validation failed',
          details: error.errors
        });
      }
      throw error;
    }
  };
};
```

## 🔄 Background Jobs & Queue System

### Job Processing with Bull Queue
```typescript
// jobs/notification.jobs.ts
import Queue from 'bull';
import { NotificationService } from '../services/notification.service';

export const notificationQueue = new Queue('notification processing', {
  redis: {
    host: process.env.REDIS_HOST,
    port: parseInt(process.env.REDIS_PORT || '6379'),
  },
});

// Job processors
notificationQueue.process('send-push-notification', async (job) => {
  const { userId, title, body, data } = job.data;
  
  await NotificationService.sendPushNotification(userId, {
    title,
    body,
    data
  });
});

notificationQueue.process('send-email', async (job) => {
  const { to, template, data } = job.data;
  
  await NotificationService.sendEmail(to, template, data);
});

// Job scheduling
export const scheduleNotification = async (
  type: 'push' | 'email',
  data: any,
  delay?: number
) => {
  const jobOptions = delay ? { delay } : {};
  
  await notificationQueue.add(`send-${type}`, data, jobOptions);
};

// Booking reminder job
export const scheduleBookingReminder = async (bookingId: string, scheduledAt: Date) => {
  const reminderTime = new Date(scheduledAt.getTime() - 24 * 60 * 60 * 1000); // 24 hours before
  
  await notificationQueue.add(
    'booking-reminder',
    { bookingId },
    { delay: reminderTime.getTime() - Date.now() }
  );
};
```

---

*This backend architecture provides a robust, scalable foundation for Freela Syria with comprehensive security, performance optimization, and maintainability features.*
